import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { viteMockServe } from 'vite-plugin-mock'
import path from 'path'
import { fileURLToPath, URL } from 'node:url'

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const isDev = command === 'serve'

  return {
    plugins: [
      vue(),
      // SVG 图标插件
      createSvgIconsPlugin({
        iconDirs: [path.resolve(process.cwd(), 'src/icons/svg')],
        symbolId: 'icon-[name]',
        inject: 'body-last',
        customDomId: '__svg__icons__dom__'
      }),
      // Mock 插件
      viteMockServe({
        mockPath: 'mock',
        localEnabled: isDev,
        prodEnabled: false,
        logger: true,
        supportTs: false
      })
    ],

    // 路径别名
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        // 兼容 Webpack 的 fallback 配置
        'path': 'path-browserify',
        'stream': 'stream-browserify'
      },
      extensions: ['.js', '.ts', '.vue', '.json']
    },

    // CSS 预处理器
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@import "@/styles/variables.scss";`
        }
      }
    },

    // 开发服务器配置
    server: {
      port: 9527,
      open: true,
      host: true,
      proxy: {
        // 如果需要代理 API 请求，可以在这里配置
      }
    },

    // 构建配置
    build: {
      outDir: 'dist',
      assetsDir: 'static',
      sourcemap: false,
      // 代码分割
      rollupOptions: {
        output: {
          manualChunks: {
            'chunk-libs': ['vue', 'vue-router', 'vuex'],
            'chunk-element-plus': ['element-plus', '@element-plus/icons-vue'],
            'chunk-echarts': ['echarts']
          }
        }
      },
      // 构建时移除 console
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      }
    },

    // 环境变量
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      // 兼容 Webpack 的 process.env
      'process.env.NODE_ENV': JSON.stringify(mode)
    },

    // 优化依赖
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'vuex',
        'element-plus',
        '@element-plus/icons-vue',
        'axios',
        'js-cookie',
        'echarts'
      ]
    }
  }
})
