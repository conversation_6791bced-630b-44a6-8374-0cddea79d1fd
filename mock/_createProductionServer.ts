import { createProdMockServer } from 'vite-plugin-mock/es/createProdMockServer'

// 导入所有的 mock 模块
const modules = import.meta.globEager('./**/*.ts')

const mockModules: any[] = []
Object.keys(modules).forEach((key) => {
  if (key.includes('/_')) {
    return
  }
  mockModules.push(...modules[key].default)
})

/**
 * 用于生产环境的 mock 服务
 */
export function setupProdMockServer() {
  createProdMockServer(mockModules)
}
