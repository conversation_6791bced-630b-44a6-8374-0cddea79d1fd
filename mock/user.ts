import { MockMethod } from 'vite-plugin-mock'

export default [
  {
    url: '/api/user/login',
    method: 'post',
    response: ({ body }) => {
      const { email, password } = body
      if (email === '<EMAIL>' && password === 'password') {
        return {
          code: 200,
          message: 'success',
          data: {
            token: 'mock-token-123456',
            user: {
              id: 1,
              name: 'Admin User',
              email: '<EMAIL>',
              avatar: '',
              roles: ['admin']
            }
          }
        }
      } else {
        return {
          code: 401,
          message: '用户名或密码错误',
          data: null
        }
      }
    }
  },
  {
    url: '/api/user/info',
    method: 'get',
    response: () => {
      return {
        code: 200,
        message: 'success',
        data: {
          id: 1,
          name: 'Admin User',
          email: '<EMAIL>',
          avatar: '',
          roles: ['admin']
        }
      }
    }
  }
] as <PERSON><PERSON>Method[]
