{"name": "galaxy-vue3-demi", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "serve": "vite", "build": "vite build", "preview": "vite preview", "test:unit": "jest", "lint": "eslint src --ext .vue,.js,.ts --fix", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@unhead/vue": "^2.0.11", "axios": "^1.10.0", "core-js": "^3.43.0", "echarts": "^5.6.0", "element-plus": "^2.10.2", "js-cookie": "^3.0.5", "normalize.css": "^8.0.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vuex": "^4.1.0"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/eslint-parser": "^7.27.5", "@types/mockjs": "^1.0.10", "@types/node": "^24.0.7", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-airbnb": "^8.0.0", "@vue/test-utils": "^2.4.6", "@vue/vue3-jest": "^27.0.0", "babel-eslint": "10.1.0", "babel-jest": "^27.5.1", "eslint": "^8.57.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.32.0", "eslint-plugin-vue": "^9.4.3", "eslint-plugin-vuejs-accessibility": "^2.4.1", "husky": "^9.1.7", "jest": "^27.5.1", "mockjs": "^1.1.0", "path-browserify": "^1.0.1", "sass": "^1.89.2", "sass-embedded": "^1.89.2", "sass-loader": "^16.0.5", "stream-browserify": "^3.0.0", "svgo": "^4.0.0", "typescript": "^5.8.3", "vite": "^5.4.19", "vite-plugin-mock": "^3.0.2", "vite-plugin-svg-icons": "^2.0.1"}}